import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning as L
from torch.optim.lr_scheduler import ReduceLROnPlateau
from typing import Dict, List, Tuple, Optional, Union, Any
import math


class SpatialAttention(nn.Module):
    def __init__(self, input_dim):
        super().__init__()
        self.query = nn.Linear(input_dim, input_dim)
        self.key = nn.Linear(input_dim, input_dim)

    def forward(self, x):
        # x: [B, T, D]
        Q = self.query(x)  # [B, T, D]
        K = self.key(x)  # [B, T, D]
        attn_scores = torch.matmul(Q, K.transpose(1, 2))  # [B, T, T]
        attn_weights = F.softmax(attn_scores, dim=-1)
        return torch.matmul(attn_weights, x)  # [B, T, D]


class TemporalAttention(nn.Module):
    def __init__(self, hidden_size):
        super().__init__()
        self.W = nn.Linear(hidden_size, hidden_size)
        self.v = nn.Linear(hidden_size, 1, bias=False)

    def forward(self, hidden_states):
        # hidden_states: [B, T, H]
        energy = torch.tanh(self.W(hidden_states))  # [B, T, H]
        scores = self.v(energy).squeeze(-1)  # [B, T]
        weights = F.softmax(scores, dim=1)
        return torch.sum(hidden_states * weights.unsqueeze(-1), dim=1)  # [B, H]


class LSTMSTAModel(nn.Module):
    def __init__(
        self,
        input_size: int,
        lstm_input_size: int,
        lstm_hidden_size: int,
        output_size: int,
        sequence_length: int,
        dropout: float = 0.1,
    ):
        super().__init__()

        # 参数验证
        assert (
            input_size == sequence_length * lstm_input_size
        ), "input_size must be sequence_length * lstm_input_size"

        # 网络结构
        self.batch_norm = nn.BatchNorm1d(input_size)
        self.input_fc = nn.Linear(input_size, input_size)
        self.spatial_attn = SpatialAttention(lstm_input_size)
        self.lstm = nn.LSTM(lstm_input_size, lstm_hidden_size, batch_first=True)
        self.temporal_attn = TemporalAttention(lstm_hidden_size)
        self.output_fc = nn.Linear(lstm_hidden_size, output_size)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # x: [B, input_size]
        x = self.batch_norm(x)
        x = self.input_fc(x)
        x = self.dropout(x)

        # 重塑为序列 [B, T, D]
        x = x.view(-1, self.sequence_length, self.lstm_input_size)

        # 空间注意力
        x = self.spatial_attn(x)

        # LSTM处理
        lstm_out, _ = self.lstm(x)  # [B, T, H]

        # 时间注意力
        attn_out = self.temporal_attn(lstm_out)

        # 输出层
        return self.output_fc(attn_out)
