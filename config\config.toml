[train]
save_data = true               # 是否保存训练数据数据
freq = "30s"                   # 数据采样频率
min_data_length = 512          # 最小数据长度要求
split_ratio = [0.8, 0.1, 0.1]  # 训练集、验证集、测试集的划分比例
normalize_method = "min-max"   # 数据归一化方法
input_len = 256                # 输入序列长度(回看窗口)
output_len = 1                 # 预测序列长度(预测步长)
rolling_len = 64               # 滚动预测长度
batch_size = 512               # 批处理大小
epochs = 256                   # 训练轮数
accelerator = "auto"           # 自动选择加速器（CPU/GPU）
devices = "auto"               # 自动选择设备数量
gradient_clip_val = 1.0        # 梯度裁剪值，防止梯度爆炸
early_stopping_patience = 20   # 早停patience，连续多少轮无改善后停止训练

[lstm]
hidden_size = 256              # 隐藏层大小
num_layers = 2                 # 网络层数
dropout = 0.1                  # Dropout比率，用于防止过拟合
bidirectional = false          # 是否使用双向网络
learning_rate = 1e-3           # 学习率
weight_decay = 1e-5            # 权重衰减，用于正则化

[furnace]
total_length = 171.1  # 炉体总长, m
ph_length = 23.08
nof_length = 30.42
rtf_length = 37.7
sf_length = 35.48
jcf_length = 44.42
weld1_max = 1200  # 焊缝位置1（距焊机）最大值
weld2_max = 518  # 焊缝位置2（距入炉密封辊）最大值

[influxdb]
host = "***********"
port = 8086
username = "root"
password = "123456"
database = "annealing_furnace"

[logging]
log_dir = "logs"
level = "INFO"
format = "[%(asctime)s] - [%(name)s] - [%(levelname)s] - %(message)s"
date_format = "%Y-%m-%d %H:%M:%S"
file_handler = true
file_path = "logs/app.log"
console_handler = true
max_days = 30
