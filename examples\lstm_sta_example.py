#!/usr/bin/env python3
"""
LSTM-STA模型使用示例

该脚本展示了如何使用优化后的LSTMSTAModel和LitLSTMSTAModel进行训练和预测。
包含了模型初始化、数据准备、训练和评估的完整流程。
"""

import torch
import torch.nn as nn
import lightning as L
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.model.lstm_sta import LSTMSTAModel, LitLSTMSTAModel


def generate_synthetic_data(
    num_samples: int = 1000,
    input_size: int = 64,
    sequence_length: int = 8,
    noise_level: float = 0.1
) -> tuple[torch.Tensor, torch.Tensor]:
    """
    生成合成时间序列数据用于演示
    
    Args:
        num_samples: 样本数量
        input_size: 输入特征维度
        sequence_length: 序列长度
        noise_level: 噪声水平
        
    Returns:
        输入数据和目标数据
    """
    # 生成基础信号
    t = torch.linspace(0, 4 * np.pi, input_size)
    
    # 创建多个正弦波组合
    base_signal = torch.sin(t) + 0.5 * torch.sin(2 * t) + 0.3 * torch.sin(3 * t)
    
    # 生成批量数据
    X = []
    y = []
    
    for i in range(num_samples):
        # 添加随机相位和幅度变化
        phase = torch.rand(1) * 2 * np.pi
        amplitude = 0.5 + torch.rand(1) * 1.0
        
        # 生成当前样本的信号
        signal = amplitude * torch.sin(t + phase) + 0.3 * torch.sin(2 * t + phase)
        
        # 添加噪声
        noise = torch.randn_like(signal) * noise_level
        signal_with_noise = signal + noise
        
        # 目标是信号的平均值（简化的预测任务）
        target = torch.mean(signal_with_noise[-sequence_length:]).unsqueeze(0)
        
        X.append(signal_with_noise)
        y.append(target)
    
    return torch.stack(X), torch.stack(y)


def create_data_loaders(
    X: torch.Tensor, 
    y: torch.Tensor, 
    batch_size: int = 32,
    train_ratio: float = 0.8
) -> tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        X: 输入数据
        y: 目标数据
        batch_size: 批次大小
        train_ratio: 训练集比例
        
    Returns:
        训练和验证数据加载器
    """
    # 划分训练和验证集
    num_train = int(len(X) * train_ratio)
    
    X_train, X_val = X[:num_train], X[num_train:]
    y_train, y_val = y[:num_train], y[num_train:]
    
    # 创建数据集
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader


def test_basic_model():
    """测试基础LSTMSTAModel"""
    print("=== 测试基础LSTMSTAModel ===")
    
    # 模型参数
    input_size = 64
    lstm_input_size = 8  # input_size必须能被lstm_input_size整除
    lstm_hidden_size = 32
    output_size = 1
    sequence_length = 8
    
    # 创建模型
    model = LSTMSTAModel(
        input_size=input_size,
        lstm_input_size=lstm_input_size,
        lstm_hidden_size=lstm_hidden_size,
        output_size=output_size,
        sequence_length=sequence_length,
        dropout=0.1
    )
    
    # 生成测试数据
    batch_size = 4
    x = torch.randn(batch_size, input_size)
    
    # 前向传播
    with torch.no_grad():
        output = model(x)
        print(f"输入形状: {x.shape}")
        print(f"输出形状: {output.shape}")
        
        # 测试注意力权重
        spatial_weights, temporal_weights = model.get_attention_weights(x)
        print(f"空间注意力权重形状: {spatial_weights.shape}")
        print(f"时间注意力权重形状: {temporal_weights.shape}")
    
    print("基础模型测试通过！\n")


def test_lightning_model():
    """测试Lightning模型"""
    print("=== 测试LitLSTMSTAModel ===")
    
    # 模型参数
    input_size = 64
    lstm_input_size = 8
    lstm_hidden_size = 32
    output_size = 1
    sequence_length = 8
    
    # 创建Lightning模型（强制使用CPU进行测试）
    lit_model = LitLSTMSTAModel(
        input_size=input_size,
        lstm_input_size=lstm_input_size,
        lstm_hidden_size=lstm_hidden_size,
        output_size=output_size,
        sequence_length=sequence_length,
        dropout=0.1,
        learning_rate=1e-3
    )
    
    # 生成数据
    X, y = generate_synthetic_data(num_samples=200, input_size=input_size)
    train_loader, val_loader = create_data_loaders(X, y, batch_size=16)
    
    # 创建训练器（强制使用CPU）
    trainer = L.Trainer(
        max_epochs=5,
        accelerator="cpu",
        devices=1,
        logger=False,
        enable_checkpointing=False,
        enable_progress_bar=True
    )
    
    # 训练模型
    print("开始训练...")
    trainer.fit(lit_model, train_loader, val_loader)
    
    # 测试模型
    print("开始测试...")
    test_results = trainer.test(lit_model, val_loader)
    print(f"测试结果: {test_results}")
    
    print("Lightning模型测试通过！\n")


def visualize_attention_weights():
    """可视化注意力权重"""
    print("=== 注意力权重可视化 ===")

    # 模型参数
    input_size = 64
    lstm_input_size = 8
    sequence_length = 8

    # 创建模型
    model = LSTMSTAModel(
        input_size=input_size,
        lstm_input_size=lstm_input_size,
        lstm_hidden_size=32,
        output_size=1,
        sequence_length=sequence_length
    )

    # 生成测试数据（使用批量大小为2避免BatchNorm问题）
    x = torch.randn(2, input_size)

    try:
        # 获取注意力权重
        with torch.no_grad():
            spatial_weights, temporal_weights = model.get_attention_weights(x)

        # 创建可视化
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # 空间注意力权重热图（取第一个样本）
        spatial_weights_np = spatial_weights[0].cpu().numpy()
        im1 = ax1.imshow(spatial_weights_np, cmap='viridis', aspect='auto')
        ax1.set_title('空间注意力权重')
        ax1.set_xlabel('特征维度')
        ax1.set_ylabel('时间步')
        plt.colorbar(im1, ax=ax1)

        # 时间注意力权重（取第一个样本）
        temporal_weights_np = temporal_weights[0].cpu().numpy()
        ax2.bar(range(len(temporal_weights_np)), temporal_weights_np)
        ax2.set_title('时间注意力权重')
        ax2.set_xlabel('时间步')
        ax2.set_ylabel('权重')

        plt.tight_layout()
        plt.savefig('attention_weights.png', dpi=150, bbox_inches='tight')
        print("注意力权重可视化已保存为 'attention_weights.png'")
        plt.close()
    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("LSTM-STA模型示例程序")
    print("=" * 50)
    
    # 设置随机种子以确保结果可重现
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 测试基础模型
        test_basic_model()
        
        # 测试Lightning模型
        test_lightning_model()
        
        # 可视化注意力权重
        visualize_attention_weights()
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
